import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useOverlay } from '@toss/use-overlay';
import { useToast } from '@/Common/useToast.tsx';

import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';

import FleetDelPopup from './FleetManagement/FMFleetDelPopup';
import FleetAddPopup from './FleetManagement/FMFleetAddPopup';
import FMAddVehiclePopup from './FleetManagement/FMAddVehiclePopup';
import FMEquipmentDelPopup from './FleetManagement/FMEquipmentDelPopup';
import MAddDriverPopup from './FleetManagement/AddDriverPopup.tsx';
import MEqSettingPopup from './FleetManagement/EqSettingPopup.tsx';
import MSendItemPopup from './FleetManagement/SendItemPopup.tsx';
import MSpecifyingFleetPopup from './FleetManagement/SpecifyingFleetPopup.tsx';

import FDDriverRegistrationPopup from './FleetDriver/FDDriverRegistrationPopup';
import FDDriverEqAddPopup from './FleetDriver/FDDriverEqAddPopup';
import FDDriverDelPopup from './FleetDriver/FDDriverDelPopup';
import FDDriverEqDelPopup from './FleetDriver/FDDriverEqDelPopup';
import DModificationDriverPopup from './FleetDriver/FDDriverModPopup';
import DShockInfoPopup from './FleetDriver/ShockInfoPopup.tsx';
import DShockManagementPopup from './FleetDriver/ShockManagementPopup.tsx';

import LocationPopup from './FleetFault/LocationPopup';
import FaultChangeStatusPopup from './FleetFault/FaultChangeStatusPopup.tsx';

import ConsumablesChangeStatusPopup from './FleetConsumables/ConsumablesChangeStatusPopup.tsx';

const UseFleetPopup = () => {
  const { t } = useTranslation();

  const navigate = useNavigate();

  const overlay = useOverlay();

  const { toast } = useToast();

  // 플릿 관리
  const openFmDeletePopup = (fleets?: number[], refreshList?: () => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <FleetDelPopup
          fleets={fleets}
          onClose={close}
          onConfirm={() => {
            if (refreshList) {
              refreshList();
            }
            close();
          }}
          isOpen={isOpen}
          title={t('DeleteFleet')}
          secondButtonText={t('Cancel')}
          buttonText={t('Delete')}
          text={t(
            'AreYouSureYouWantToDeleteThisFleetDeletedInformationCannotBeRecovered',
          )}
        />
      );
    });
  };
  const openFmFleetAddPopup = (refreshList?: () => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <FleetAddPopup
          onClose={close}
          onConfirm={() => {
            close();
            if (refreshList) {
              refreshList();
            }
          }}
          isOpen={isOpen}
        />
      );
    });
  };
  const openFMAddVehiclePopup = (fleetId: number, refreshList?: () => void) => {
    overlay.open((p) => (
      <FMAddVehiclePopup
        fleetId={fleetId}
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          refreshList?.();
          p.close();
        }}
      />
    ));
  };
  const openFMEquipmentDelPopup = (
    fleetId: number,
    equipmentIds: number[],
    refreshList?: () => void,
  ) => {
    overlay.open(({ isOpen, close }) => (
      <FMEquipmentDelPopup
        fleetId={fleetId}
        equipmentIds={equipmentIds}
        onClose={close}
        onConfirm={() => {
          if (refreshList) refreshList();
          close();
        }}
        isOpen={isOpen}
        title={t('RemoveVehicle')}
        secondButtonText={t('Cancel')}
        buttonText={t('Delete')}
        text={t('AreYouSureYouWantToRemoveTheSelectedEquipmentFromTheFleet')}
      />
    ));
  };
  const openMAddDriverPopup = () => {
    overlay.open((p) => (
      <MAddDriverPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('ANewDriverHasBeenRegistered'),
          });
          p.close();
        }}
      />
    ));
  };
  const openFmDriverDeletePopup = () => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          toast({
            types: 'warning',
            description: t('TheDriverHasBeenRemoved'),
          });
          close();
        }}
        isOpen={isOpen}
        text={t('AreYouSureYouWantToRemoveTheSelectedDriverFromTheEquipment')}
        buttonText={t('Cancel')}
        secondButtonText={t('Delete')}
      />
    ));
  };
  const openMEqSettingPopup = () => {
    overlay.open((p) => (
      <MEqSettingPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({ description: 'test2' });
          p.close();
        }}
      />
    ));
  };
  const openMSendItemPopup = () => {
    overlay.open((p) => (
      <MSendItemPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({ description: 'test2' });
          p.close();
        }}
      />
    ));
  };
  const openMSpecifyingFleetPopup = () => {
    overlay.open((p) => (
      <MSpecifyingFleetPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('TheFleetHasBeenAssigned'),
          });
          p.close();
        }}
      />
    ));
  };

  // 운전자 관리
  const openFDPageOutPopup = (refreshList?: () => void) => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          // toast({
          //   types: 'success',
          //   description: t('DriverHasBeenRegistered'),
          // });
          refreshList?.();
          close();
        }}
        isOpen={isOpen}
        title={t('LeaveThisPage')}
        text={t(
          'YouHaveUnsavedChangesIfYouLeaveNowAllEnteredInformationWillBeLostTheMaintenanceStatusWillNotBeUpdatedToCompletedUnlessTheReportIsSubmitted',
        )}
        buttonText={t('Stay')}
        secondButtonText={t('Leave')}
      />
    ));
  };
  const openFDDriverRegistrationPopup = (options?: {
    onManualRegister?: () => void;
  }) => {
    overlay.open((p) => (
      <FDDriverRegistrationPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('DriverListHasBeenSuccessfullyUploaded'),
          });
          p.close();
        }}
        onManualRegister={options?.onManualRegister}
      />
    ));
  };
  const openFDDriverEqAddPopup = (
    driverId: number,
    refreshList?: () => void,
  ) => {
    overlay.open((p) => (
      <FDDriverEqAddPopup
        driverId={driverId}
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          refreshList?.();
          p.close();
        }}
      />
    ));
  };
  const openFDDriverDelPopup = (
    driverIds: number[],
    refreshList?: () => void,
  ) => {
    overlay.open(({ isOpen, close }) => (
      <FDDriverDelPopup
        driverIds={driverIds}
        onClose={close}
        onConfirm={() => {
          refreshList?.();
          close();
        }}
        isOpen={isOpen}
        title={t('DeleteDriver')}
        secondButtonText={t('Cancel')}
        buttonText={t('Delete')}
        text={t(
          'AreYouSureYouWantToDeleteTheSelectedDriverDeletedInformationCannotBeRecovered',
        )}
      />
    ));
  };
  const openFDDriverEqDelPopup = (
    driverId: number,
    equipmentIds: number[],
    refreshList?: () => void,
  ) => {
    overlay.open(({ isOpen, close }) => (
      <FDDriverEqDelPopup
        driverId={driverId}
        equipmentIds={equipmentIds}
        onClose={close}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('TheSelectedVehicleHasBeenUnassignedFromDriver'),
          });
          refreshList?.();
          close();
        }}
        isOpen={isOpen}
        title={t('RemoveVehicle')}
        secondButtonText={t('Cancel')}
        buttonText={t('Delete')}
        text={t(
          'AreYouSureYouWantToDeleteTheSelectedEquipmentAssignedToThisDriver',
        )}
      />
    ));
  };
  const openFDDriverModPopup = () => {
    overlay.open((p) => (
      <DModificationDriverPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('DriverInformationHasBeenUpdated'),
          });
          p.close();
        }}
      />
    ));
  };
  const openDShockInfoPopup = () => {
    overlay.open((p) => (
      <DShockInfoPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({ description: 'test2' });
          p.close();
        }}
      />
    ));
  };
  const openDShockManagementPopup = () => {
    overlay.open((p) => (
      <DShockManagementPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('TheUpdatedInformationHasBeenSaved'),
          });
          p.close();
        }}
      />
    ));
  };
  const openDImpactDeletePopup = (refreshList?: () => void) => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          toast({
            types: 'success',
            description: t('TheSelectedImpactInformationHasBeenDeleted'),
          });
          refreshList?.();
          close();
        }}
        isOpen={isOpen}
        title={t('DeleteImpactInformation')}
        text={t(
          'AreYouSureYouWantToDeleteTheSelectedImpactInformationDeletedDataCannotBeRecovered',
        )}
        buttonText={t('Cancel')}
        secondButtonText={t('Delete')}
      />
    ));
  };

  // 차량 관리
  const openFvBackPopup = (refreshList?: () => void) => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          refreshList?.();
          close();
        }}
        isOpen={isOpen}
        title={t('LeaveThisPage')}
        text={t(
          'YouHaveUnsavedChangesIfYouLeaveThisPageNowAllUnsavedDataWillBeLost',
        )}
        buttonText={t('Cancel')}
        secondButtonText={t('Leave')}
      />
    ));
  };

  // 고장 관리
  const openFFLocationPopup = () => {
    overlay.open((p) => (
      <LocationPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          p.close();
        }}
      />
    ));
  };
  const openFFFaultChangeStatusPopup = () => {
    overlay.open((p) => (
      <FaultChangeStatusPopup
        isOpen={p.isOpen}
        onClose={p.close}
        onConfirm={(selected) => {
          if (selected === '3') {
            navigate('/fleet-fault-maintenance');
          }
          p.close();
        }}
      />
    ));
  };
  const openFFMaintenanceOutPopup = (
    onConfirm: (close: () => void) => void,
  ) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          onClose={close}
          onConfirm={() => {
            onConfirm(close);
            toast({
              types: 'success',
              description: t('MaintenanceReportSubmitted'),
            });
            close();
          }}
          isOpen={isOpen}
          title={t('LeaveThisPage')}
          text={t(
            'YouHaveUnsavedChangesIfYouLeaveNowAllEnteredInformationWillBeLostTheMaintenanceStatusWillNotBeUpdatedToCompletedUnlessTheReportIsSubmitted',
          )}
          buttonText={t('Stay')}
          secondButtonText={t('Leave')}
        />
      );
    });
  };

  // 소모품 관리
  const openFCConsumablesChangeStatusPopup = () => {
    overlay.open((p) => (
      <ConsumablesChangeStatusPopup
        isOpen={p.isOpen}
        onClose={() => p.close()}
        onConfirm={() => {
          p.close();
        }}
      />
    ));
  };

  return {
    openFmDeletePopup,
    openFmFleetAddPopup,
    openFMAddVehiclePopup,
    openFMEquipmentDelPopup,
    openMAddDriverPopup,
    openFmDriverDeletePopup,
    openMEqSettingPopup,
    openMSendItemPopup,
    openMSpecifyingFleetPopup,
    openFDPageOutPopup,
    openFDDriverRegistrationPopup,
    openFDDriverEqAddPopup,
    openFDDriverDelPopup,
    openFDDriverEqDelPopup,
    openFDDriverModPopup,
    openDShockInfoPopup,
    openDShockManagementPopup,
    openDImpactDeletePopup,
    openFvBackPopup,
    openFFLocationPopup,
    openFFFaultChangeStatusPopup,
    openFFMaintenanceOutPopup,
    openFCConsumablesChangeStatusPopup,
  };
};

export default UseFleetPopup;
