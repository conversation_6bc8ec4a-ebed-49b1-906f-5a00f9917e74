import React from 'react';
import VehicleSearchFilter from './VehicleSearchFilter';
import TrackingSearchFilter from './TrackingSearchFilter';
import { EquipmentType } from '@/types/EquipmentType';

interface SearchFilterProps {
  mode?: 'vehicle' | 'tracking';
  left?: string;
  style?: React.CSSProperties;
  filteredType?: string;
  filteredSort?: string;
  onResult: (result: EquipmentType.FilteredMapItem[]) => void;
}

/**
 * 장비 필터 조회
 */
const SearchFilter: React.FC<SearchFilterProps> = ({
  mode,
  left,
  style,
  filteredType,
  filteredSort,
  onResult,
}) => {
  console.log('mode', mode);
  console.log('filteredType', filteredType);
  console.log('filteredSort', filteredSort);
  if (mode === 'tracking') {
    return (
      <TrackingSearchFilter
        left={left}
        style={style}
        filteredType={filteredType}
        filteredSort={filteredSort}
        onResult={onResult}
      />
    );
  } else {
    return (
      <VehicleSearchFilter left={left} style={style} onResult={onResult} />
    );
  }
};
SearchFilter.displayName = 'SearchFilter';
export default SearchFilter;
