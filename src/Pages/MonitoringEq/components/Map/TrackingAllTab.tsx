import { useRef, useState, useEffect, useCallback } from 'react';
import { VariableSizeList as List } from 'react-window';
import Dropdown from '@/Common/Components/common/DropDown';
import FilteredItem from './FilteredItem';
import { useTranslation } from 'react-i18next';
import { EquipmentType } from '@/types/EquipmentType';
import { on } from 'events';
import { set } from 'react-hook-form';

interface TrackingAllTabProps {
  items: EquipmentType.FilteredMapItem[];
  onListItemClick?: (item: EquipmentType.FilteredMapItem) => void;
  onFilteredTypeItemClick?: (item: string) => void;
  onFilteredSortItemClick?: (item: string) => void;
  onFilteredSort?: (item: string) => void;
}

const TrackingAllTab = ({
  items,
  onListItemClick,
  onFilteredTypeItemClick,
  onFilteredSortItemClick,
  onFilteredSort,
}: TrackingAllTabProps) => {
  const { t } = useTranslation();

  // 정렬 옵션 및 상태
  const sortOptions = [
    { key: t('Model'), value: 'modelName' },
    { key: t('VehicleNo'), value: 'plateNo' },
    { key: t('OperatingHour'), value: 'mileage' },
  ];
  const orderOptions = [
    { key: t('AscendingOrder'), value: 'asc' },
    { key: t('DescendingOrder'), value: 'desc' },
  ];
  const [sortSel, setSortSel] = useState({
    key: t('Model'),
    value: 'modelName',
  });
  const [orderSel, setOrderSel] = useState({
    key: t('DescendingOrder'),
    value: 'desc',
  });

  // 리스트 상태 및 정렬 함수
  const [sortedItems, setSortedItems] = useState<
    EquipmentType.FilteredMapItem[]
  >([]);

  // const compareSort = (
  //   a: (string | number)[],
  //   b: (string | number)[],
  //   asc: boolean,
  // ) => {
  //   for (let i = 0; i < a.length; i++) {
  //     if (a[i] !== b[i]) {
  //       return asc ? (a[i] > b[i] ? 1 : -1) : a[i] < b[i] ? 1 : -1;
  //     }
  //   }
  //   return 0;
  // };

  // const compareSortList = (type: string, asc: boolean) => {
  //   const sorted = [...items].sort((a, b) => {
  //     switch (type) {
  //       case 'MODEL':
  //         return compareSort(
  //           [a.modelName, a.plateNo],
  //           [b.modelName, b.plateNo],
  //           asc,
  //         );
  //       case 'MILEAGE':
  //         return compareSort([a.mileage], [b.mileage], asc);
  //       case 'LOCATION':
  //         return compareSort([a.location], [b.location], asc);
  //       case 'LASTUPDATE':
  //       default:
  //         return compareSort([a.lastUpdate], [b.lastUpdate], asc);
  //     }
  //   });
  //   setSortedItems(sorted);
  // };

  // useEffect(() => {
  //   compareSortList(sortSel.value, orderSel.value === 'ASC');
  //   setSortedItems(items);
  // }, [items, sortSel, orderSel]);

  useEffect(() => {
    setSortedItems(items);
  }, [items]);

  const handleChangeSortFilter = (key: string, value: string) => {
    setSortSel({ key, value });
    onFilteredTypeItemClick?.(value);
    onFilteredSort(sortSel.value + ',' + orderSel.value);
  };
  const handleChangeOrderFilter = (key: string, value: string) => {
    setOrderSel({ key, value });
    onFilteredSortItemClick?.(value);
  };

  const listContainer = useRef<HTMLDivElement>(null);
  const listRef = useRef<List>(null);
  const [width, setWidth] = useState(0);
  const [height, setHeight] = useState(0);
  const heightMap = useRef<{ [key: number]: number }>({});
  const getItemHeight = useCallback(
    (index: number) => heightMap.current[index] ?? 168,
    [],
  );

  useEffect(() => {
    if (!listContainer.current) return;
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setWidth(entry.contentRect.width);
        setHeight(entry.contentRect.height);
      }
    });
    observer.observe(listContainer.current);
    setWidth(listContainer.current.offsetWidth);
    setHeight(listContainer.current.offsetHeight);
    return () => observer.disconnect();
  }, []);

  const Row = ({
    index,
    style,
  }: {
    index: number;
    style: React.CSSProperties;
  }) => {
    const rowRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      const measuredHeight = rowRef.current?.getBoundingClientRect().height;
      if (measuredHeight && measuredHeight !== heightMap.current[index]) {
        heightMap.current[index] = measuredHeight;
        listRef.current?.resetAfterIndex(index);
      }
    }, [sortedItems[index]]);

    return (
      <div
        ref={rowRef}
        style={{ ...style, height: 'auto', paddingBottom: '4px' }}
      >
        <FilteredItem item={sortedItems[index]} onItemClick={onListItemClick} />
      </div>
    );
  };

  return (
    <>
      <div className="py-4 px-5 f-c gap-[10px] border-b border-gray-6">
        <Dropdown
          placeholder={sortSel.key}
          options={sortOptions}
          onSelPair={handleChangeSortFilter}
          size="sm"
        />
        <Dropdown
          placeholder={orderSel.key}
          options={orderOptions}
          onSelPair={handleChangeOrderFilter}
          size="sm"
        />
      </div>
      {sortedItems.length === 0 && <FilteredItem key={0} />}
      <div ref={listContainer} className="h-screen py-1 px-2">
        <List
          ref={listRef}
          width={width}
          height={height}
          itemCount={sortedItems.length}
          itemSize={getItemHeight}
          overscanCount={5}
        >
          {Row}
        </List>
      </div>
    </>
  );
};

export default TrackingAllTab;
